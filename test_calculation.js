// 测试金额计算逻辑
const testData = {
  tableData: [
    { id: '1', qty: 100, unitPrice: 10.5, amount: 0 },
    { id: '2', qty: 200, unitPrice: 15.2, amount: 0 }
  ],
  editableData: {
    '1': { qty: 150, unitPrice: 10.5, amount: 0 }
  }
};

// 模拟 totalCount 函数
function totalCount(row) {
  const qty = parseFloat(row.qty);
  const unitPrice = parseFloat(row.unitPrice);
  
  if (isNaN(qty) || isNaN(unitPrice)) {
    return 0;
  }
  
  return Math.round(qty * unitPrice * 100000) / 100000;
}

// 模拟 inputTotal 函数
function inputTotal(id) {
  const editableData = testData.editableData;
  const tableData = testData.tableData;
  
  if(editableData[id].unitPrice !== null && editableData[id].qty !== null && editableData[id].qty !== '' && editableData[id].qty !== undefined){
    editableData[id].amount = totalCount(editableData[id]);
  }
  if(editableData[id].qty === undefined || editableData[id].qty === null || editableData[id].qty === ''){
    editableData[id].amount = 0;
  }
  
  // 同步更新 tableData 中对应行的金额
  const record = tableData.find(item => item.id === id);
  if (record) {
    record.amount = editableData[id].amount;
  }
  
  // 重新计算汇总数据
  calculateTotals();
}

// 模拟 calculateTotals 函数
function calculateTotals() {
  const editableData = testData.editableData;
  const tableData = testData.tableData;
  
  let qtyTotal = 0;
  let decTotal = 0;

  tableData.forEach(item => {
    // 优先使用编辑数据，如果没有则使用原始数据
    const currentData = editableData[item.id] || item;
    
    // 计划数量汇总
    if (currentData.qty) {
      const qty = parseFloat(currentData.qty);
      if (!isNaN(qty)) {
        qtyTotal += qty;
      }
    }

    // 计划总金额汇总
    if (currentData.amount) {
      const amount = parseFloat(currentData.amount);
      if (!isNaN(amount)) {
        decTotal += amount;
      }
    }
  });

  console.log('汇总结果:', { qtyTotal, decTotal });
  return { qtyTotal, decTotal };
}

// 测试
console.log('初始数据:', testData);
console.log('修改数量前汇总:');
calculateTotals();

console.log('\n修改第一行数量后:');
inputTotal('1');
console.log('更新后数据:', testData);
